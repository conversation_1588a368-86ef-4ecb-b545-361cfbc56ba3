from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from typing import Any, Dict
import uvicorn

app = FastAPI()

# 定义请求体模型（可选，用于更好的类型验证）
class RequestData(BaseModel):
    # 这里可以根据实际需要定义具体的字段
    # 如果不确定数据结构，可以使用 Dict[str, Any]
    pass

@app.post("/")
async def handle_post(data: Dict[str, Any]):
    """
    处理POST请求
    """ 
    if data:

        print(f"Received data: {data}")
        return {"status": "success"}

if __name__ == '__main__':
    uvicorn.run(app, host='0.0.0.0', port=5002)
